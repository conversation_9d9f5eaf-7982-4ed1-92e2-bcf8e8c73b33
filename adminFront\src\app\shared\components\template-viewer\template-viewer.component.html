<div class="template-viewer-modal">
  <div class="template-viewer-header d-flex justify-content-between align-items-center mb-3">
    <h5 class="mb-0">模板清單</h5>
    <div>
      <button class="btn btn-outline-primary btn-sm mr-2" (click)="showCreateTemplate = !showCreateTemplate">
        <i class="fas fa-plus mr-1"></i>從共用資料建立模板
      </button>
      <button class="btn btn-success btn-sm" (click)="onAddTemplate()">
        <i class="fas fa-plus mr-1"></i>新增
      </button>
    </div>
  </div>

  <!-- 建立模板區塊 -->
  <div *ngIf="showCreateTemplate" class="mb-3 p-2 border rounded bg-light">
    <form (ngSubmit)="createTemplate()">
      <div class="form-group mb-2">
        <label>模板名稱</label>
        <input type="text" class="form-control" [(ngModel)]="newTemplateName" name="templateName" required
          maxlength="30">
      </div>
      <div class="form-group mb-2">
        <label>模板描述</label>
        <input type="text" class="form-control" [(ngModel)]="newTemplateDesc" name="templateDesc" maxlength="100">
      </div>
      <div class="form-group mb-2">
        <label>選擇要存成模板的資料</label>
        <div style="max-height:120px;overflow:auto;">
          <div *ngFor="let item of sharedData; let i = index">
            <input type="checkbox" [checked]="item.selected" (change)="item.selected = $any($event.target).checked"
              name="item{{i}}"> {{item.CRequirement}}
            ({{item.CGroupName}})
          </div>
        </div>
      </div>
      <button class="btn btn-primary btn-sm mr-2" type="submit">儲存模板</button>
      <button class="btn btn-secondary btn-sm" type="button" (click)="showCreateTemplate = false">取消</button>
    </form>
  </div>

  <div class="template-list">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th>模板名稱</th>
          <th>描述</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let tpl of templates">
          <td>{{ tpl.TemplateName }}</td>
          <td>{{ tpl.Description }}</td>
          <td>
            <button class="btn btn-info btn-sm mr-1" (click)="onSelectTemplate(tpl)">查看</button>
            <button class="btn btn-danger btn-sm" (click)="onDeleteTemplate(tpl.TemplateID!)"
              *ngIf="tpl.TemplateID">刪除</button>
          </td>
        </tr>
        <tr *ngIf="!templates || templates.length === 0">
          <td colspan="3" class="text-center">暫無模板</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- 查看模板細節 -->
  <div *ngIf="selectedTemplate" class="mt-3 p-2 border rounded bg-white">
    <h6>模板細節：{{selectedTemplate!.TemplateName}}</h6>
    <ul>
      <li *ngFor="let detail of currentTemplateDetails">
        {{detail.FieldName}}: {{detail.FieldValue}}
      </li>
    </ul>
    <button class="btn btn-secondary btn-sm" (click)="closeTemplateDetail()">關閉</button>
  </div>
</div>